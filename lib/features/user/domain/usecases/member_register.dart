import 'dart:developer' as developer;

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import 'package:mcdc/features/user/data/datasources/auth_storage_data_source.dart';
import 'package:mcdc/features/user/domain/entities/member_register_request.dart';
import 'package:mcdc/features/user/domain/entities/member_register_response.dart';
import 'package:mcdc/features/user/domain/repositories/user_repository.dart';

/// Parameters for member registration
class MemberRegisterParams extends Equatable {
  final MemberRegisterRequest request;

  const MemberRegisterParams({required this.request});

  @override
  List<Object> get props => [request];
}

/// Use case for member registration
class MemberRegister
    extends UseCase<MemberRegisterResponse, MemberRegisterParams> {
  final UserRepository _userRepository;
  final AuthStorageDataSource _authStorage;

  MemberRegister(this._userRepository, this._authStorage);

  @override
  Future<Either<Failure, MemberRegisterResponse>> call(
    MemberRegisterParams params,
  ) async {
    developer.log('MemberRegister: ${params.request.toString()}');
    MemberRegisterRequest transformedRequest = MemberRegisterRequest(
      identityCardNo: params.request.identityCardNo,
      username: params.request.username,
      password: params.request.password,
      confirmPassword: params.request.confirmPassword,
      email: params.request.email,
      firstName: params.request.firstName,
      lastName: params.request.lastName,
      phone: params.request.phone,
      appMasMemberTypeId: params.request.appMasMemberTypeId,
      appMasGovernmentSectorId:
          params.request.appMasGovernmentSectorId == 0
              ? null
              : params.request.appMasGovernmentSectorId,
      appMasGovernmentSectorOther: params.request.appMasGovernmentSectorOther,
      appMasMinistryId:
          params.request.appMasMinistryId == 0
              ? null
              : params.request.appMasMinistryId,
      appMasMinistryOther: params.request.appMasMinistryOther,
      appMasDepartmentId:
          params.request.appMasDepartmentId == 0
              ? null
              : params.request.appMasDepartmentId,
      appMasDepartmentOther: params.request.appMasDepartmentOther,
      name: params.request.name,
      isNotification: params.request.isNotification,
      lang: params.request.lang,
      status: params.request.status,
      otpToken: params.request.otpToken,
    );

    final result = await _userRepository.memberRegister(
      request: transformedRequest,
    );

    // Save login data to secure storage if registration is successful
    return result.fold((failure) => Left(failure), (response) async {
      if (response.isSuccess && response.loginData != null) {
        try {
          await _authStorage.saveLoginData(response.loginData!);
          return Right(response);
        } catch (e) {
          // If saving fails, still return success but log the error
          // The user is registered, but data might not be persisted
          return Right(response);
        }
      } else {
        return Right(response);
      }
    });
  }
}
